import { motion } from 'framer-motion';
import { BotIcon, CodeIcon, BuildingIcon, MonitorIcon } from 'lucide-react';
import Image from 'next/image';
import { Card, CardContent } from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ServiceType } from '@/components/service-selector';
import { memo } from 'react';
import { withBasePath } from '@/lib/base-path';

interface OverviewProps {
  selectedModelId?: string;
  selectedService?: ServiceType;
}

// 서비스별 챗봇 설정 정의
const serviceConfigs = {
  geon: {
    icon: BuildingIcon,
    title: 'LX 공유재산 위탁관리 업무 도우미',
    description: '공유재산 위탁관리 편람을 기반으로 업무를 도와드립니다.',
    accordionTitle: '지원 업무 범위',
    sections: [
      { title: "공유재산 관리" },
      { title: "업무 절차 안내" },
      { title: "법령 및 규정" },
      { title: "공유재산 일반" }
    ],
    footerText: 'LX 공유재산 업무에 대한 전문적인 도움을 제공해드립니다.'
  },
  manual: {
    icon: MonitorIcon,
    title: 'LX 재산관리 통합체계 사용 도우미',
    description: 'LX 공유재산 통합체계 매뉴얼을 바탕으로 사용법을 안내해 드립니다.',
    accordionTitle: '지원 업무 범위',
    sections: [
      { title: "재산관리" },
      { title: "실태조사" },
      { title: "민원관리" },
      { title: "주제도" },
      { title: "지도 서비스" }
    ],
    footerText: 'LX 공유재산 통합체계 사용에 도움을 제공해드립니다.'
  }
};

const PureOverview = ({ selectedModelId, selectedService = 'geon' }: OverviewProps) => {
  // 선택된 서비스에 따른 설정 사용
  const config = serviceConfigs[selectedService];
  const IconComponent = config.icon;

  return (
    <motion.div
      key={`lx-chatbot-overview-${selectedService}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.3 }}
      className="w-full max-w-sm backdrop-blur-sm mx-auto bg-gradient-to-b from-background/10 to-background/80 rounded-xl"
    >
      <Card className="border-none shadow-none bg-transparent">
        <CardContent className="p-6 space-y-6">
          <div className="relative">
            <motion.div
              className="flex items-center justify-center gap-4"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 150 }}
            >
              <Image
              // basePath 가 적용이 되어야하는데 안됨. 이유??
                src={withBasePath('/images/lx-logo.svg')} 
                alt="LX" 
                width={60} 
                height={30}
                className="text-primary dark:invert"
              />
              <span className="font-bold text-2xl text-primary">+</span>
              <BotIcon size={28} className="text-primary" />
            </motion.div>
            <motion.div
              className="text-center mt-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <h2 className="text-lg font-semibold bg-clip-text bg-gradient-to-r from-primary to-primary/80">
                {config.title}
              </h2>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="space-y-4"
          >
            <p className="text-sm text-center">
              {config.description}
            </p>

            <Accordion type="single" collapsible className="flex justify-center bg-background/40 rounded-lg">
              <AccordionItem value="examples" className="border-none">
                <AccordionTrigger className="justify-center gap-2 py-3 px-4 hover:no-underline hover:bg-blue-700 hover:text-white hover:border-blue-700 transition-all duration-300 rounded-lg mx-2 data-[state=open]:bg-blue-700 data-[state=open]:text-white data-[state=open]:border-blue-700 shadow-sm hover:shadow-md group bg-white border-2 border-gray-200">
                  <span className="text-sm font-medium text-gray-800 group-hover:text-white group-data-[state=open]:text-white group-hover:font-semibold transition-all duration-200">{config.accordionTitle}</span>
                </AccordionTrigger>
                <AccordionContent className="py-4">
                  <motion.div
                    className="space-y-6"
                    variants={{
                      hidden: { opacity: 0 },
                      show: {
                        opacity: 1,
                        transition: { staggerChildren: 0.1 }
                      }
                    }}
                    initial="hidden"
                    animate="show"
                  >
                    {config.sections.map((section, idx) => (
                      <motion.div
                        key={idx}
                        variants={{
                          hidden: { opacity: 0, y: 10 },
                          show: { opacity: 1, y: 0 }
                        }}
                        className="space-y-2"
                      >
                        <h3 className="text-sm font-medium text-primary">{section.title}</h3>
                      </motion.div>
                    ))}
                  </motion.div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </motion.div>

          <motion.p
            className="text-xs text-center text-muted-foreground"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            {config.footerText}
          </motion.p>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export const Overview = memo(PureOverview, (prevProps, nextProps) => {
  // selectedService가 변경되면 리렌더링
  if (prevProps.selectedService !== nextProps.selectedService) return false;
  if (prevProps.selectedModelId !== nextProps.selectedModelId) return false;
  
  return true;
});

export default Overview;