
/** @type {import('next').NextConfig} */
const nextConfig = {
    output: "standalone", // for self-hosting

    basePath: process.env.NEXT_BASE_PATH || '/nlpm-svc2d-front-gypc-map/aiPop',
    // assetPrefix: process.env.NEXT_ASSET_PREFIX || '/nlpm-svc2d-front-gypc-map/ai',

    env: {
        NEXT_PUBLIC_BASE_PATH: process.env.NEXT_BASE_PATH || '/nlpm-svc2d-front-gypc-map/aiPop',
    },

    eslint: {
        // 빌드 시 ESLint 오류를 무시 (개발 중에는 IDE에서 확인)
        ignoreDuringBuilds: true,
    },
    pageExtensions: ['js', 'jsx', 'mdx', 'ts', 'tsx'],

};

export default nextConfig

