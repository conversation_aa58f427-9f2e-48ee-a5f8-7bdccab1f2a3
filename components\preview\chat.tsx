"use client";

import type { Attachment, Message } from "ai";
import { useChat } from "ai/react";
import { useState, useEffect } from "react";
import { useWindowSize } from "usehooks-ts";
import { withBasePath } from "@/lib/base-path";

import { ChatHeader } from "@/components/preview/chat-header";
import { ServiceType } from "@/components/service-selector";

import { MultimodalInput } from "@/components/multimodal-input";
import { Messages } from "@/components/preview/messages";
import { VisibilityType } from "@/components/visibility-selector";
import { FooterText } from "../footer";
import { PreviewPanel } from "../preview-panel";
import { usePreview } from "@/lib/hooks/use-preview";

export function Chat({
  id,
  initialMessages,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
}: {
  id: string;
  initialMessages: Array<Message>;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
}) {
  const [conversationId, setConversationId] = useState<string | undefined>(undefined);
  const [selectedServiceId, setSelectedServiceId] = useState<ServiceType>('geon');

  const {
    messages,
    input,
    setInput,
    setMessages,
    handleSubmit,
    append,
    isLoading,
    stop,
    reload,
    data,
    metadata,
    status,
    error
  } = useChat({
    api: selectedServiceId === 'manual' ? withBasePath("/api/manual-chat") : withBasePath("/api/dev-chat"),
    id,
    body: {
      id,
      modelId: selectedModelId,
      conversationId: conversationId
    },
    initialMessages
  });

  useEffect(() => {
    if (data && data.length > 0) {
      const conversationData = data.find(item =>
        typeof item === 'object' && item !== null && 'type' in item && item.type === 'conversation-id'
      );
      if (conversationData && typeof conversationData === 'object' && 'id' in conversationData) {
        if (conversationData.id !== null) {
          setConversationId(conversationData.id as string);
          console.log('대화 ID 저장됨:', conversationData.id);
        }
      }
    }
  }, [data]);

  const { width: windowWidth = 1920, height: windowHeight = 1080 } =
    useWindowSize();

  const { preview } = usePreview();

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);

  return (
    <div className="flex flex-row min-w-0 h-dvh overflow-hidden bg-background">
      <div className="flex flex-col min-w-0 w-full overflow-hidden">
        <ChatHeader
          chatId={id}
          selectedServiceId={selectedServiceId}
          onServiceChange={(serviceId) => {
            setSelectedServiceId(serviceId);
            // 서비스 변경 시 대화 초기화
            setMessages([]);
            setConversationId(undefined);
          }}
          isReadonly={isReadonly}
        />

        <Messages
          chatId={id}
          isLoading={isLoading}
          votes={[]}
          messages={messages}
          setMessages={setMessages}
          reload={reload}
          append={append}
          isReadonly={isReadonly}
          selectedModelId={selectedModelId}
          selectedService={selectedServiceId}
          status={status}
          error={error}
        />

        <form className="flex mx-auto px-4 bg-background gap-2 w-full md:max-w-3xl">
          {!isReadonly && (
            <MultimodalInput
              chatId={id}
              input={input}
              setInput={setInput}
              handleSubmit={handleSubmit}
              isLoading={isLoading}
              stop={stop}
              attachments={attachments}
              setAttachments={setAttachments}
              messages={messages}
              setMessages={setMessages}
              append={append}
              selectedModelId={selectedModelId}
            />
          )}
        </form>
        <FooterText className="py-2" />
      </div>

      <PreviewPanel isReadonly={isReadonly} isVisible={preview?.isVisible} />
    </div>
  );
}
